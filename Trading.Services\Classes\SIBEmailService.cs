using Amazon.Runtime;
using DotLiquid.Tags;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SendGrid;
using brevo_csharp.Api;
using brevo_csharp.Client;
using brevo_csharp.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Net.Mail;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Reflection;
using System.Text.RegularExpressions;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Comms;
using Trading.API.Data.Models;
using Trading.Services.ExternalDTO;
using Trading.Services.Interfaces;
using Configuration = brevo_csharp.Client.Configuration;
using Trading.API.Data.Enums;

namespace Trading.Services.Classes;

public class SIBEmailService : IEmailService
{
  private class MailboxLayerResponse
  {
    public string email { get; set; }
    public string did_you_mean { get; set; }
    public string user { get; set; }
    public string domain { get; set; }
    public bool format_valid { get; set; }
    public bool mx_found { get; set; }
    public bool smtp_check { get; set; }
    public bool catch_all { get; set; }
    public bool role { get; set; }
    public bool disposable { get; set; }
    public bool free { get; set; }
    public float score { get; set; }
  }

  private class MailboxLayerFailResponse
  {
    public bool success { get; set; }
    public MailboxLayerError error { get; set; }
  }

  private class MailboxLayerError
  {
    public int code { get; set; }
    public string type { get; set; }
    public string email { get; set; }
  }

  private readonly IFileService _fileService;
  private readonly TradingContext _tradingContext;
  AccountApi _apiInstance;
  EmailConfigDTO _config;
  protected readonly IOptionsSnapshot<CommsEmailSettingsDTO> _emailSettings;

  public SIBEmailService(
    IOptionsSnapshot<CommsEmailSettingsDTO> emailSettings,
    IFileService fileService,
    IOptionsSnapshot<EmailConfigDTO> configuration,
    TradingContext tradingContext)
  {
    _fileService = fileService;
    _config = configuration.Value;
    _emailSettings = emailSettings;
    _tradingContext = tradingContext;

    // Clear any existing API keys and set the correct one
    //lock (Configuration.Default.ApiKey) // lock to ensure thread safety
    //{
    //  Configuration.Default.ApiKey.Clear();
    //  Configuration.Default.ApiKey.Add("api-key", _config.APIKey);
    //}

    Configuration.Default.ApiKey.Clear();
    Configuration.Default.ApiKey["api-key"] = _config.APIKey;
  }

  public async Task<bool> ValidateEmail(string emailAddress)
  {
    string apiKey = _config.MailboxLayerAPIKey;
    string requestUrl = $"http://apilayer.net/api/check?access_key={apiKey}&email={emailAddress}";

    using (HttpClient client = new HttpClient())
    {
      HttpResponseMessage response = await client.GetAsync(requestUrl);

      if (response.IsSuccessStatusCode)
      {
        string apiResponse = await response.Content.ReadAsStringAsync();
        var json = JsonConvert.DeserializeObject<MailboxLayerFailResponse>(apiResponse);

        if (json != null && json.success)
        {
          return true;
        }
        else if (json != null)
        {
          var jsonRes = JsonConvert.DeserializeObject<MailboxLayerResponse>(apiResponse);
          if (jsonRes != null && jsonRes.format_valid)
          {
            return true;
          }
        }
      }
    }

    return false;
  }

  public async Task<bool> SendEmail(SendEmailDTO dto, CancellationToken cancellationToken)
  {
    var apiInstance = new TransactionalEmailsApi();

    SendSmtpEmailSender Email = new SendSmtpEmailSender(_config.SenderName, _config.Sender);
    SendSmtpEmailTo smtpEmailTo = new SendSmtpEmailTo(dto.ToEmail, dto.ToName);

    List<SendSmtpEmailTo> To = new List<SendSmtpEmailTo>();
    To.Add(smtpEmailTo);

    string HtmlContent = dto.EmailBody;
    string TextContent = "E-mail content text";
    string Subject = dto.Subject;
    string ReplyToName = "No Reply";
    string ReplyToEmail = _emailSettings.Value.FromAddress ?? "<EMAIL>";

    SendSmtpEmailReplyTo ReplyTo = new SendSmtpEmailReplyTo(ReplyToEmail, ReplyToName);


    try
    {
      if (dto.Tags == null || dto.Tags.Count == 0)
      {
        dto.Tags = new List<string> { "default" }; // Ensure at least one tag is present
      }

      var sendSmtpEmail = new SendSmtpEmail(Email, To, null, null, HtmlContent, TextContent, Subject, ReplyTo, tags: dto.Tags);
      CreateSmtpEmail result = await apiInstance.SendTransacEmailAsync(sendSmtpEmail);
      Debug.WriteLine(result.ToJson());
      Console.WriteLine(result.ToJson());

      return true;
    }
    catch (Exception e)
    {
      Debug.WriteLine(e.Message);
      Console.WriteLine(e.Message);
      return false;
    }
  }

  public async Task<bool> TestSendEmail(CancellationToken cancellationToken)
  {
    SendEmailDTO dto = new SendEmailDTO
    {
      Subject = "Test Subject",
      EmailBody = "Test email from Remarq trading platform",
      ToEmail = "<EMAIL>",
      ToName = "Dave",
      EmailTitle = "TEST TITLE"
    };

    await SendEmail(dto, cancellationToken);
    return true;
  }


}